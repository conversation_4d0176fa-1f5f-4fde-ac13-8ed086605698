<?php
/**
 * Admin Panel - Payment Verification
 * Verify and manage customer payments for custom ads
 */

require_once 'includes/auth.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

$page_title = "Payment Verification";
$success = '';
$error = '';

// Handle payment verification
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['verify_payment'])) {
        $payment_id = (int)$_POST['payment_id'];
        $action = $_POST['action']; // 'approve' or 'reject'
        $admin_notes = trim($_POST['admin_notes'] ?? '');
        
        if ($action === 'approve') {
            // Start transaction
            mysqli_begin_transaction($conn);
            
            try {
                // Update payment status
                $stmt = $conn->prepare("UPDATE customer_payments SET payment_status = 'verified', admin_notes = ?, verified_by = ?, verified_at = NOW() WHERE id = ?");
                $stmt->bind_param("sii", $admin_notes, $_SESSION['admin_id'], $payment_id);
                $stmt->execute();
                
                // Get payment details
                $stmt = $conn->prepare("
                    SELECT cp.*, ap.duration_days, ap.max_ads, ca.whatsapp_number 
                    FROM customer_payments cp 
                    JOIN ad_packages ap ON cp.package_id = ap.id 
                    JOIN customer_accounts ca ON cp.customer_id = ca.id 
                    WHERE cp.id = ?
                ");
                $stmt->bind_param("i", $payment_id);
                $stmt->execute();
                $payment = $stmt->get_result()->fetch_assoc();
                
                if ($payment) {
                    // Update customer total spent
                    $stmt = $conn->prepare("UPDATE customer_accounts SET total_spent = total_spent + ?, total_orders = total_orders + 1 WHERE id = ?");
                    $stmt->bind_param("di", $payment['amount'], $payment['customer_id']);
                    $stmt->execute();
                    
                    // Update provider total spent if exists
                    $stmt = $conn->prepare("UPDATE custom_ads_providers SET total_spent = total_spent + ? WHERE whatsapp_number = ?");
                    $stmt->bind_param("ds", $payment['amount'], $payment['whatsapp_number']);
                    $stmt->execute();
                    
                    // Auto-approve ads if setting is enabled
                    $auto_approve = getSetting($conn, 'auto_approve_ads', false);
                    if ($auto_approve) {
                        // This would be implemented when creating ads
                        // For now, just log the approval
                    }
                }
                
                mysqli_commit($conn);
                $success = "Payment approved successfully!";
                
            } catch (Exception $e) {
                mysqli_rollback($conn);
                $error = "Failed to approve payment: " . $e->getMessage();
            }
            
        } elseif ($action === 'reject') {
            $stmt = $conn->prepare("UPDATE customer_payments SET payment_status = 'rejected', admin_notes = ?, verified_by = ?, verified_at = NOW() WHERE id = ?");
            $stmt->bind_param("sii", $admin_notes, $_SESSION['admin_id'], $payment_id);
            
            if ($stmt->execute()) {
                $success = "Payment rejected successfully!";
            } else {
                $error = "Failed to reject payment.";
            }
        }
    }
}

// Get pending payments
$pending_payments = [];
$stmt = $conn->prepare("
    SELECT 
        cp.*,
        ca.whatsapp_number,
        ca.customer_name,
        ap.package_name,
        ap.duration_days,
        pm.display_name as payment_method_name
    FROM customer_payments cp
    JOIN customer_accounts ca ON cp.customer_id = ca.id
    JOIN ad_packages ap ON cp.package_id = ap.id
    LEFT JOIN payment_methods pm ON cp.payment_method = pm.method_code
    WHERE cp.payment_status = 'pending'
    ORDER BY cp.created_at DESC
");
$stmt->execute();
$pending_payments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Get recent verified payments
$recent_payments = [];
$stmt = $conn->prepare("
    SELECT 
        cp.*,
        ca.whatsapp_number,
        ca.customer_name,
        ap.package_name,
        pm.display_name as payment_method_name
    FROM customer_payments cp
    JOIN customer_accounts ca ON cp.customer_id = ca.id
    JOIN ad_packages ap ON cp.package_id = ap.id
    LEFT JOIN payment_methods pm ON cp.payment_method = pm.method_code
    WHERE cp.payment_status IN ('verified', 'rejected')
    ORDER BY cp.verified_at DESC
    LIMIT 20
");
$stmt->execute();
$recent_payments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Get payment statistics
$stats = [];
$stats_result = mysqli_query($conn, "
    SELECT 
        payment_status,
        COUNT(*) as count,
        SUM(amount) as total_amount
    FROM customer_payments 
    GROUP BY payment_status
");
while ($row = mysqli_fetch_assoc($stats_result)) {
    $stats[$row['payment_status']] = $row;
}

// Helper function to get setting
function getSetting($conn, $key, $default = null) {
    $stmt = $conn->prepare("SELECT setting_value FROM custom_ads_settings WHERE setting_key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    return $result ? $result['setting_value'] : $default;
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Payment Verification</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Payment Verification</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="text-warning"><?php echo $stats['pending']['count'] ?? 0; ?></h4>
                            <p class="text-muted mb-0">Pending Payments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ri-time-line text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="text-success"><?php echo $stats['verified']['count'] ?? 0; ?></h4>
                            <p class="text-muted mb-0">Verified Payments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ri-check-line text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="text-danger"><?php echo $stats['rejected']['count'] ?? 0; ?></h4>
                            <p class="text-muted mb-0">Rejected Payments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ri-close-line text-danger" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="text-info">৳<?php echo number_format($stats['verified']['total_amount'] ?? 0, 2); ?></h4>
                            <p class="text-muted mb-0">Total Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="ri-money-dollar-circle-line text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Payments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-time-line me-2"></i>Pending Payments 
                        <span class="badge bg-warning ms-2"><?php echo count($pending_payments); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($pending_payments)): ?>
                        <div class="text-center py-4">
                            <i class="ri-check-double-line text-success" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No Pending Payments</h5>
                            <p class="text-muted">All payments have been processed.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Package</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Transaction ID</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pending_payments as $payment): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($payment['customer_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($payment['whatsapp_number']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($payment['package_name']); ?><br>
                                                <small class="text-muted"><?php echo $payment['duration_days']; ?> days</small>
                                            </td>
                                            <td>
                                                <strong><?php echo $payment['currency']; ?> <?php echo number_format($payment['amount'], 2); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo htmlspecialchars($payment['payment_method_name'] ?? $payment['payment_method']); ?></span>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                                <?php if ($payment['payment_proof']): ?>
                                                    <br><small class="text-muted" title="<?php echo htmlspecialchars($payment['payment_proof']); ?>">
                                                        <i class="ri-file-text-line"></i> Has proof
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y g:i A', strtotime($payment['created_at'])); ?><br>
                                                <small class="text-muted"><?php echo time_elapsed_string($payment['created_at']); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-success" onclick="verifyPayment(<?php echo $payment['id']; ?>, 'approve', '<?php echo htmlspecialchars($payment['customer_name']); ?>')">
                                                        <i class="ri-check-line"></i> Approve
                                                    </button>
                                                    <button class="btn btn-danger" onclick="verifyPayment(<?php echo $payment['id']; ?>, 'reject', '<?php echo htmlspecialchars($payment['customer_name']); ?>')">
                                                        <i class="ri-close-line"></i> Reject
                                                    </button>
                                                    <button class="btn btn-info" onclick="viewPaymentDetails(<?php echo htmlspecialchars(json_encode($payment)); ?>)">
                                                        <i class="ri-eye-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="ri-history-line me-2"></i>Recent Processed Payments
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Package</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Processed</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_payments as $payment): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($payment['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($payment['package_name']); ?></td>
                                        <td><?php echo $payment['currency']; ?> <?php echo number_format($payment['amount'], 2); ?></td>
                                        <td>
                                            <?php if ($payment['payment_status'] === 'verified'): ?>
                                                <span class="badge bg-success">Verified</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Rejected</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('M j, g:i A', strtotime($payment['verified_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Verification Modal -->
<div class="modal fade" id="verificationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verificationModalTitle">Verify Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="verificationForm">
                <input type="hidden" name="payment_id" id="verification_payment_id">
                <input type="hidden" name="action" id="verification_action">
                <div class="modal-body">
                    <div id="verification_customer_info"></div>
                    <div class="mb-3">
                        <label class="form-label">Admin Notes</label>
                        <textarea class="form-control" name="admin_notes" rows="3" placeholder="Add notes about this verification..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="verify_payment" class="btn" id="verification_submit_btn">Verify</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function verifyPayment(paymentId, action, customerName) {
    document.getElementById('verification_payment_id').value = paymentId;
    document.getElementById('verification_action').value = action;
    
    const modal = document.getElementById('verificationModal');
    const title = document.getElementById('verificationModalTitle');
    const submitBtn = document.getElementById('verification_submit_btn');
    const customerInfo = document.getElementById('verification_customer_info');
    
    if (action === 'approve') {
        title.textContent = 'Approve Payment';
        submitBtn.textContent = 'Approve Payment';
        submitBtn.className = 'btn btn-success';
        customerInfo.innerHTML = '<div class="alert alert-success">Approving payment for: <strong>' + customerName + '</strong></div>';
    } else {
        title.textContent = 'Reject Payment';
        submitBtn.textContent = 'Reject Payment';
        submitBtn.className = 'btn btn-danger';
        customerInfo.innerHTML = '<div class="alert alert-danger">Rejecting payment for: <strong>' + customerName + '</strong></div>';
    }
    
    new bootstrap.Modal(modal).show();
}

function viewPaymentDetails(payment) {
    // Implementation for viewing payment details
    alert('Payment Details:\n' + JSON.stringify(payment, null, 2));
}

function time_elapsed_string(datetime) {
    // Simple time elapsed function - you can implement this in PHP instead
    return 'recently';
}
</script>

<?php 
// Simple time elapsed function
function time_elapsed_string($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => 'year',
        'm' => 'month',
        'w' => 'week',
        'd' => 'day',
        'h' => 'hour',
        'i' => 'minute',
        's' => 'second',
    );
    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
        } else {
            unset($string[$k]);
        }
    }

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) . ' ago' : 'just now';
}

include 'includes/footer.php'; 
?>
