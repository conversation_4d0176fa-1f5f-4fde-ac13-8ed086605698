-- Enhanced Custom Ads System Database Schema
-- Version: 1.0
-- Created: 2025-06-02

-- 1. Custom Ads Providers Table
CREATE TABLE IF NOT EXISTS custom_ads_providers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    provider_id VARCHAR(50) UNIQUE NOT NULL,
    whatsapp_number VARCHAR(20) UNIQUE NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    status ENUM('active', 'suspended', 'pending') DEFAULT 'pending',
    total_spent DECIMAL(10,2) DEFAULT 0.00,
    total_ads INT DEFAULT 0,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    password_hash VARCHAR(255) NOT NULL,
    verification_token VARCHAR(100),
    is_verified BOOLEAN DEFAULT FALSE,
    INDEX idx_provider_id (provider_id),
    INDEX idx_whatsapp (whatsapp_number),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Ad Packages Table
CREATE TABLE IF NOT EXISTS ad_packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    package_name VARCHAR(100) NOT NULL,
    duration_days INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'BDT',
    max_ads INT DEFAULT 1,
    features JSON,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_duration (duration_days)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Customer Accounts Table
CREATE TABLE IF NOT EXISTS customer_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    whatsapp_number VARCHAR(20) UNIQUE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    total_spent DECIMAL(10,2) DEFAULT 0.00,
    total_orders INT DEFAULT 0,
    status ENUM('active', 'suspended', 'blocked') DEFAULT 'active',
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP NULL,
    verification_code VARCHAR(6),
    verification_expires TIMESTAMP NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    INDEX idx_whatsapp (whatsapp_number),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Customer Payments Table
CREATE TABLE IF NOT EXISTS customer_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    package_id INT NOT NULL,
    transaction_id VARCHAR(100) NOT NULL,
    payment_method ENUM('bkash', 'nagad', 'rocket', 'google_pay', 'paypal', 'manual') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'BDT',
    payment_status ENUM('pending', 'verified', 'rejected', 'refunded') DEFAULT 'pending',
    payment_proof TEXT,
    admin_notes TEXT,
    verified_by INT NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customer_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES ad_packages(id) ON DELETE RESTRICT,
    INDEX idx_customer (customer_id),
    INDEX idx_status (payment_status),
    INDEX idx_method (payment_method),
    INDEX idx_transaction (transaction_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Enhanced Custom Ads Table (modify existing)
ALTER TABLE custom_ads 
ADD COLUMN IF NOT EXISTS provider_id INT NULL,
ADD COLUMN IF NOT EXISTS customer_id INT NULL,
ADD COLUMN IF NOT EXISTS package_id INT NULL,
ADD COLUMN IF NOT EXISTS payment_id INT NULL,
ADD COLUMN IF NOT EXISTS url_type ENUM('website', 'playstore', 'appstore', 'other') DEFAULT 'website',
ADD COLUMN IF NOT EXISTS button_text VARCHAR(50) DEFAULT 'Visit',
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS is_approved BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS approved_by INT NULL,
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS priority INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS target_audience JSON,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Add foreign keys to custom_ads
ALTER TABLE custom_ads
ADD FOREIGN KEY (provider_id) REFERENCES custom_ads_providers(id) ON DELETE SET NULL,
ADD FOREIGN KEY (customer_id) REFERENCES customer_accounts(id) ON DELETE SET NULL,
ADD FOREIGN KEY (package_id) REFERENCES ad_packages(id) ON DELETE SET NULL,
ADD FOREIGN KEY (payment_id) REFERENCES customer_payments(id) ON DELETE SET NULL;

-- Add indexes to custom_ads
ALTER TABLE custom_ads
ADD INDEX idx_provider (provider_id),
ADD INDEX idx_customer (customer_id),
ADD INDEX idx_expires (expires_at),
ADD INDEX idx_approved (is_approved),
ADD INDEX idx_url_type (url_type);

-- 6. Ad Analytics Table
CREATE TABLE IF NOT EXISTS ad_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ad_id INT NOT NULL,
    event_type ENUM('view', 'click', 'install', 'conversion') NOT NULL,
    user_ip VARCHAR(45),
    user_agent TEXT,
    device_info JSON,
    location_data JSON,
    referrer VARCHAR(500),
    session_id VARCHAR(100),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ad_id) REFERENCES custom_ads(id) ON DELETE CASCADE,
    INDEX idx_ad_event (ad_id, event_type),
    INDEX idx_timestamp (timestamp),
    INDEX idx_session (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. Payment Methods Configuration Table
CREATE TABLE IF NOT EXISTS payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method_name VARCHAR(50) NOT NULL,
    method_code VARCHAR(20) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    instructions TEXT,
    account_details JSON,
    is_active BOOLEAN DEFAULT TRUE,
    min_amount DECIMAL(10,2) DEFAULT 0.00,
    max_amount DECIMAL(10,2) DEFAULT 999999.99,
    processing_fee_percent DECIMAL(5,2) DEFAULT 0.00,
    processing_fee_fixed DECIMAL(10,2) DEFAULT 0.00,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_code (method_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 8. System Settings for Custom Ads
CREATE TABLE IF NOT EXISTS custom_ads_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
