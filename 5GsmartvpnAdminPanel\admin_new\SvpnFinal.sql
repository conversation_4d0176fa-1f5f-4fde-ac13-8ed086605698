-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 01, 2025 at 05:47 PM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `5gsmartvpnnewupdate`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `admin_username` varchar(255) DEFAULT NULL,
  `action` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `role` enum('admin','moderator','viewer') DEFAULT 'admin',
  `status` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `password`, `email`, `role`, `status`, `created_at`) VALUES
(1, 'admin', '12345', '<EMAIL>', 'admin', 1, '2025-05-26 22:17:05');

-- --------------------------------------------------------

--
-- Table structure for table `contact_messages`
--

CREATE TABLE `contact_messages` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `status` enum('unread','read','replied','archived') NOT NULL DEFAULT 'unread',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `category` varchar(50) DEFAULT 'general',
  `reply_text` text DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `read_at` datetime DEFAULT NULL,
  `replied_at` datetime DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact_messages`
--

INSERT INTO `contact_messages` (`id`, `name`, `email`, `subject`, `message`, `status`, `priority`, `category`, `reply_text`, `admin_id`, `read_at`, `replied_at`, `ip_address`, `user_agent`, `created_at`, `updated_at`) VALUES
(1, 'John Doe', '<EMAIL>', 'Connection Issue', 'I am having trouble connecting to the VPN servers. Can you help?', 'replied', 'normal', 'technical', 'wer', NULL, '2025-05-27 07:21:02', '2025-05-27 07:21:22', '*************', NULL, '2025-05-27 07:20:52', '2025-05-27 07:21:22');

-- --------------------------------------------------------

--
-- Table structure for table `custom_ads`
--

CREATE TABLE `custom_ads` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `text` text DEFAULT NULL,
  `url` text DEFAULT NULL,
  `date_start` date DEFAULT NULL,
  `date_end` date DEFAULT NULL,
  `on` tinyint(4) NOT NULL DEFAULT 1,
  `view_count` int(11) NOT NULL DEFAULT 0,
  `click_count` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `custom_ads`
--

INSERT INTO `custom_ads` (`id`, `title`, `image`, `text`, `url`, `date_start`, `date_end`, `on`, `view_count`, `click_count`) VALUES
(1, 'Testing notification', 'images/bannerBg_1741973854.png', 'tee', 'https://google.com', '2025-04-26', '2025-04-28', 1, 6, 2),
(2, 'yrdy', 'images/bannerBg_1742111924.png', 'yrrr', 'https://google.com', '2025-03-16', '2025-03-17', 1, 28, 4);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `status` enum('pending','sent','error','scheduled','completed') NOT NULL DEFAULT 'pending',
  `sent_to` varchar(50) DEFAULT 'all',
  `notification_type` varchar(50) DEFAULT 'general',
  `schedule_type` enum('immediate','scheduled','recurring') DEFAULT 'immediate',
  `scheduled_time` datetime DEFAULT NULL,
  `recurring_interval` enum('daily','weekly','monthly') DEFAULT NULL,
  `next_run_time` datetime DEFAULT NULL,
  `last_run_time` datetime DEFAULT NULL,
  `response` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `category` varchar(50) DEFAULT 'general',
  `target_audience` varchar(100) DEFAULT 'all_users',
  `delivery_count` int(11) DEFAULT 0,
  `success_count` int(11) DEFAULT 0,
  `failure_count` int(11) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Triggers `notifications`
--
DELIMITER $$
CREATE TRIGGER `before_notification_insert` BEFORE INSERT ON `notifications` FOR EACH ROW BEGIN
    IF NEW.schedule_type IN ('scheduled', 'recurring') THEN
        SET NEW.next_run_time = NEW.scheduled_time;
        SET NEW.status = 'scheduled';
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_notification_update` BEFORE UPDATE ON `notifications` FOR EACH ROW BEGIN
    IF NEW.schedule_type IN ('scheduled', 'recurring') AND
       (OLD.scheduled_time != NEW.scheduled_time OR OLD.schedule_type != NEW.schedule_type) THEN
        SET NEW.next_run_time = NEW.scheduled_time;
        IF NEW.status = 'sent' THEN
            SET NEW.status = 'scheduled';
        END IF;
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `notification_logs`
--

CREATE TABLE `notification_logs` (
  `id` int(11) NOT NULL,
  `notification_id` int(11) NOT NULL,
  `action` varchar(50) NOT NULL,
  `details` text DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notification_settings`
--

CREATE TABLE `notification_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notification_settings`
--

INSERT INTO `notification_settings` (`id`, `setting_key`, `setting_value`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'fcm_server_key', '', 'Firebase Cloud Messaging Server Key', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(2, 'fcm_sender_id', '', 'Firebase Cloud Messaging Sender ID', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(3, 'default_topic', 'all', 'Default topic for notifications', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(4, 'max_daily_notifications', '100', 'Maximum notifications per day', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(5, 'notification_retention_days', '30', 'Days to keep notification history', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(6, 'enable_scheduled_notifications', '1', 'Enable scheduled notification processing', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(7, 'enable_recurring_notifications', '1', 'Enable recurring notifications', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(8, 'notification_sound', 'default', 'Default notification sound', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(9, 'notification_icon', 'ic_notification', 'Default notification icon', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(10, 'notification_color', '#3b82f6', 'Default notification color', 1, '2025-05-26 23:40:10', '2025-05-26 23:40:10');

-- --------------------------------------------------------

--
-- Table structure for table `notification_templates`
--

CREATE TABLE `notification_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `category` varchar(50) DEFAULT 'general',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `target_audience` varchar(100) DEFAULT 'all_users',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `is_active` tinyint(1) DEFAULT 1,
  `usage_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notification_templates`
--

INSERT INTO `notification_templates` (`id`, `name`, `title`, `message`, `category`, `priority`, `target_audience`, `data`, `is_active`, `usage_count`, `created_at`, `updated_at`) VALUES
(1, 'Welcome Message', 'Welcome to 5G Smart VPN!', 'Thank you for joining us. Enjoy secure and fast VPN connections.', 'welcome', 'normal', 'new_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(2, 'Server Maintenance', 'Scheduled Maintenance Notice', 'We will be performing scheduled maintenance on our servers. Service may be temporarily unavailable.', 'maintenance', 'high', 'all_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(3, 'New Server Added', 'New VPN Server Available', 'A new high-speed VPN server has been added to your location. Try it now!', 'update', 'normal', 'all_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10'),
(4, 'Security Alert', 'Security Update Required', 'Please update your app to the latest version for enhanced security features.', 'security', 'urgent', 'all_users', NULL, 1, 0, '2025-05-26 23:40:10', '2025-05-26 23:40:10');

-- --------------------------------------------------------

--
-- Table structure for table `servers`
--

CREATE TABLE `servers` (
  `id` int(11) NOT NULL,
  `name` text NOT NULL,
  `username` text NOT NULL,
  `password` text NOT NULL,
  `configFile` text NOT NULL,
  `flagURL` varchar(200) NOT NULL,
  `type` int(11) NOT NULL DEFAULT 1,
  `pos` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(4) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `servers`
--

INSERT INTO `servers` (`id`, `name`, `username`, `password`, `configFile`, `flagURL`, `type`, `pos`, `status`) VALUES
(2, 'Singapore', 'muxCxgnYNPsfpSVAYGw2g2bD', 'Wduuca75VAXfMm8zfHGu9Pyk', 'client\r\ndev tun\r\nproto udp\r\nremote sg-sng.prod.surfshark.com 1194\r\nremote-random\r\nnobind\r\ntun-mtu 1500\r\nmssfix 1450\r\nping 15\r\nping-restart 0\r\nreneg-sec 0\r\n\r\nremote-cert-tls server\r\n\r\nauth-user-pass\r\n\r\n#comp-lzo\r\nverb 3\r\nfast-io\r\ncipher AES-256-CBC\r\n\r\nauth SHA512\r\n\r\n<ca>\r\n-----BEGIN CERTIFICATE-----\r\nMIIFTTCCAzWgAwIBAgIJAMs9S3fqwv+mMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNV\r\nBAYTAlZHMRIwEAYDVQQKDAlTdXJmc2hhcmsxGjAYBgNVBAMMEVN1cmZzaGFyayBS\r\nb290IENBMB4XDTE4MDMxNDA4NTkyM1oXDTI4MDMxMTA4NTkyM1owPTELMAkGA1UE\r\nBhMCVkcxEjAQBgNVBAoMCVN1cmZzaGFyazEaMBgGA1UEAwwRU3VyZnNoYXJrIFJv\r\nb3QgQ0EwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDEGMNj0aisM63o\r\nSkmVJyZPaYX7aPsZtzsxo6m6p5Wta3MGASoryRsBuRaH6VVa0fwbI1nw5ubyxkua\r\nNa4v3zHVwuSq6F1p8S811+1YP1av+jqDcMyojH0ujZSHIcb/i5LtaHNXBQ3qN48C\r\nc7sqBnTIIFpmb5HthQ/4pW+a82b1guM5dZHsh7q+LKQDIGmvtMtO1+NEnmj81BAp\r\nFayiaD1ggvwDI4x7o/Y3ksfWSCHnqXGyqzSFLh8QuQrTmWUm84YHGFxoI1/8AKdI\r\nyVoB6BjcaMKtKs/pbctk6vkzmYf0XmGovDKPQF6MwUekchLjB5gSBNnptSQ9kNgn\r\nTLqi0OpSwI6ixX52Ksva6UM8P01ZIhWZ6ua/T/tArgODy5JZMW+pQ1A6L0b7egIe\r\nghpwKnPRG+5CzgO0J5UE6gv000mqbmC3CbiS8xi2xuNgruAyY2hUOoV9/BuBev8t\r\ntE5ZCsJH3YlG6NtbZ9hPc61GiBSx8NJnX5QHyCnfic/X87eST/amZsZCAOJ5v4EP\r\nSaKrItt+HrEFWZQIq4fJmHJNNbYvWzCE08AL+5/6Z+lxb/Bm3dapx2zdit3x2e+m\r\niGHekuiE8lQWD0rXD4+T+nDRi3X+kyt8Ex/8qRiUfrisrSHFzVMRungIMGdO9O/z\r\nCINFrb7wahm4PqU2f12Z9TRCOTXciQIDAQABo1AwTjAdBgNVHQ4EFgQUYRpbQwyD\r\nahLMN3F2ony3+UqOYOgwHwYDVR0jBBgwFoAUYRpbQwyDahLMN3F2ony3+UqOYOgw\r\nDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAgEAn9zV7F/XVnFNZhHFrt0Z\r\nS1Yqz+qM9CojLmiyblMFh0p7t+Hh+VKVgMwrz0LwDH4UsOosXA28eJPmech6/bjf\r\nymkoXISy/NUSTFpUChGO9RabGGxJsT4dugOw9MPaIVZffny4qYOc/rXDXDSfF2b+\r\n303lLPI43y9qoe0oyZ1vtk/UKG75FkWfFUogGNbpOkuz+et5Y0aIEiyg0yh6/l5Q\r\n5h8+yom0HZnREHhqieGbkaGKLkyu7zQ4D4tRK/mBhd8nv+09GtPEG+D5LPbabFVx\r\nKjBMP4Vp24WuSUOqcGSsURHevawPVBfgmsxf1UCjelaIwngdh6WfNCRXa5QQPQTK\r\nubQvkvXONCDdhmdXQccnRX1nJWhPYi0onffvjsWUfztRypsKzX4dvM9k7xnIcGSG\r\nEnCC4RCgt1UiZIj7frcCMssbA6vJ9naM0s7JF7N3VKeHJtqe1OCRHMYnWUZt9vrq\r\nX6IoIHlZCoLlv39wFW9QNxelcAOCVbD+19MZ0ZXt7LitjIqe7yF5WxDQN4xru087\r\nFzQ4Hfj7eH1SNLLyKZkA1eecjmRoi/OoqAt7afSnwtQLtMUc2bQDg6rHt5C0e4dC\r\nLqP/9PGZTSJiwmtRHJ/N5qYWIh9ju83APvLm/AGBTR2pXmj9G3KdVOkpIC7L35dI\r\n623cSEC3Q3UZutsEm/UplsM=\r\n-----END CERTIFICATE-----\r\n</ca>\r\n\r\nkey-direction 1\r\n\r\n<tls-auth>\r\n-----BEGIN OpenVPN Static key V1-----\r\nb02cb1d7c6fee5d4f89b8de72b51a8d0\r\nc7b282631d6fc19be1df6ebae9e2779e\r\n6d9f097058a31c97f57f0c35526a44ae\r\n09a01d1284b50b954d9246725a1ead1f\r\nf224a102ed9ab3da0152a15525643b2e\r\nee226c37041dc55539d475183b889a10\r\ne18bb94f079a4a49888da566b9978346\r\n0ece01daaf93548beea6c827d9674897\r\ne7279ff1a19cb092659e8c1860fbad0d\r\nb4ad0ad5732f1af4655dbd66214e552f\r\n04ed8fd0104e1d4bf99c249ac229ce16\r\n9d9ba22068c6c0ab742424760911d463\r\n6aafb4b85f0c952a9ce4275bc821391a\r\na65fcd0d2394f006e3fba0fd34c4bc4a\r\nb260f4b45dec3285875589c97d3087c9\r\n134d3a3aa2f904512e85aa2dc2202498\r\n-----END OpenVPN Static key V1-----\r\n</tls-auth>', 'singapore.png', 0, 0, 1);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'admob_app_id', 'ca-app-pub-3940256099942544~3347511713', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(2, 'admob_banner_id', 'ca-app-pub-3940256099942544/6300978111', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(3, 'admob_interstitial_id', 'ca-app-pub-3940256099942544/1033173712', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(4, 'admob_rewarded_id', 'ca-app-pub-3940256099942544/5224354917', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(5, 'admob_native_id', 'ca-app-pub-3940256099942544/2247696110', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(6, 'banner_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(7, 'interstitial_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(8, 'rewarded_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(9, 'native_enabled', '1', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(10, 'test_mode', '0', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(11, 'click_limit', '5', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(12, 'show_frequency', '3', '2025-05-26 20:37:45', '2025-06-01 21:43:46'),
(13, 'admob_openad_id', 'ca-app-pub-3940256099942544ddfds', '2025-05-27 02:49:00', '2025-06-01 21:43:46'),
(14, 'openad_enabled', '1', '2025-05-27 02:49:00', '2025-06-01 21:43:46'),
(15, 'facebook_app_id', '', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(16, 'facebook_banner_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(17, 'facebook_interstitial_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(18, 'facebook_rewarded_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(19, 'facebook_native_id', 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(20, 'banner_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(21, 'interstitial_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(22, 'rewarded_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(23, 'native_type', 'admob', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(24, 'reward_time', '30', '2025-05-27 02:53:14', '2025-06-01 21:43:46'),
(25, 'app_name', '5G Smart VPN', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(26, 'app_version', '1.0.0', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(27, 'app_description', 'Fast and secure VPN service', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(28, 'contact_email', '<EMAIL>', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(29, 'support_url', 'https://5gsmartvpn.com/support', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(30, 'privacy_policy_url', 'https://5gsmartvpn.com/privacy', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(31, 'terms_of_service_url', 'https://5gsmartvpn.com/terms', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(32, 'maintenance_mode', '0', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(33, 'debug_mode', '0', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(34, 'auto_connect', '1', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(35, 'kill_switch', '1', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(36, 'dns_leak_protection', '1', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(37, 'connection_timeout', '30', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(38, 'max_concurrent_connections', '20', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(39, 'log_level', 'info', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(40, 'backup_frequency', 'daily', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(41, 'notification_email', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(42, 'smtp_host', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(43, 'smtp_port', '587', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(44, 'smtp_username', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(45, 'smtp_password', '', '2025-05-27 06:25:43', '2025-05-27 06:25:43'),
(46, 'smtp_encryption', 'tls', '2025-05-27 06:25:43', '2025-05-27 06:25:43');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `fk_admin_id` (`admin_id`);

--
-- Indexes for table `custom_ads`
--
ALTER TABLE `custom_ads`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_schedule` (`schedule_type`,`scheduled_time`),
  ADD KEY `idx_next_run` (`next_run_time`),
  ADD KEY `idx_status_schedule` (`status`,`schedule_type`,`scheduled_time`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_priority` (`priority`),
  ADD KEY `idx_target_audience` (`target_audience`);

--
-- Indexes for table `notification_logs`
--
ALTER TABLE `notification_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_notification_id` (`notification_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_key` (`setting_key`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `notification_templates`
--
ALTER TABLE `notification_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `servers`
--
ALTER TABLE `servers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_logs`
--
ALTER TABLE `admin_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `contact_messages`
--
ALTER TABLE `contact_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `custom_ads`
--
ALTER TABLE `custom_ads`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `notification_logs`
--
ALTER TABLE `notification_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notification_settings`
--
ALTER TABLE `notification_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `notification_templates`
--
ALTER TABLE `notification_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `servers`
--
ALTER TABLE `servers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD CONSTRAINT `fk_contact_messages_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `notification_logs`
--
ALTER TABLE `notification_logs`
  ADD CONSTRAINT `notification_logs_ibfk_1` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
