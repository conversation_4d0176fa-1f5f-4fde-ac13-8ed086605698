<?php
/**
 * Custom Ads Packages Page
 * Display available packages and handle package purchases
 */

session_start();
require_once '../admin_new/includes/config.php';

// Check if provider is logged in
if (!isset($_SESSION['provider_id'])) {
    header("Location: login.php");
    exit;
}

// Get provider information
$stmt = $conn->prepare("SELECT * FROM custom_ads_providers WHERE provider_id = ?");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();
$provider = $stmt->get_result()->fetch_assoc();

// Get available packages
$stmt = $conn->prepare("SELECT * FROM ad_packages WHERE is_active = 1 ORDER BY display_order ASC, price ASC");
$stmt->execute();
$packages = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Get payment methods
$stmt = $conn->prepare("SELECT * FROM payment_methods WHERE is_active = 1 ORDER BY display_order ASC");
$stmt->execute();
$payment_methods = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

$success = '';
$error = '';

// Handle package purchase
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['purchase_package'])) {
    $package_id = (int)$_POST['package_id'];
    $payment_method = $_POST['payment_method'];
    $transaction_id = trim($_POST['transaction_id']);
    $payment_proof = trim($_POST['payment_proof'] ?? '');
    
    if (empty($transaction_id)) {
        $error = 'Please provide a transaction ID.';
    } else {
        // Get package details
        $stmt = $conn->prepare("SELECT * FROM ad_packages WHERE id = ? AND is_active = 1");
        $stmt->bind_param("i", $package_id);
        $stmt->execute();
        $package = $stmt->get_result()->fetch_assoc();
        
        if (!$package) {
            $error = 'Invalid package selected.';
        } else {
            // Check if customer account exists, create if not
            $stmt = $conn->prepare("SELECT id FROM customer_accounts WHERE whatsapp_number = ?");
            $stmt->bind_param("s", $provider['whatsapp_number']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                // Create customer account
                $stmt = $conn->prepare("INSERT INTO customer_accounts (whatsapp_number, customer_name, email, is_verified) VALUES (?, ?, ?, 1)");
                $stmt->bind_param("sss", $provider['whatsapp_number'], $provider['contact_person'], $provider['email']);
                $stmt->execute();
                $customer_id = $conn->insert_id;
            } else {
                $customer_id = $result->fetch_assoc()['id'];
            }
            
            // Check if transaction ID already exists
            $stmt = $conn->prepare("SELECT id FROM customer_payments WHERE transaction_id = ?");
            $stmt->bind_param("s", $transaction_id);
            $stmt->execute();
            
            if ($stmt->get_result()->num_rows > 0) {
                $error = 'This transaction ID has already been used.';
            } else {
                // Insert payment record
                $stmt = $conn->prepare("INSERT INTO customer_payments (customer_id, package_id, transaction_id, payment_method, amount, currency, payment_status, payment_proof) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?)");
                $stmt->bind_param("iissdss", $customer_id, $package_id, $transaction_id, $payment_method, $package['price'], $package['currency'], $payment_proof);
                
                if ($stmt->execute()) {
                    $success = 'Payment submitted successfully! Your payment is being verified and you will be notified once approved.';
                } else {
                    $error = 'Failed to submit payment. Please try again.';
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad Packages - 5G Smart VPN Custom Ads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .package-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        .package-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        .package-card.featured {
            border: 3px solid #667eea;
            position: relative;
        }
        .package-card.featured::before {
            content: "Most Popular";
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .price {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li i {
            color: #28a745;
            margin-right: 10px;
        }
        .payment-modal .modal-content {
            border-radius: 20px;
        }
        .payment-method-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method-card:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        .payment-method-card.selected {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-box me-2"></i>Ad Packages</h2>
                        <p class="text-muted">Choose the perfect package for your advertising needs</p>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Packages Grid -->
                <div class="row">
                    <?php foreach ($packages as $index => $package): ?>
                        <?php 
                        $features = json_decode($package['features'], true) ?? [];
                        $isFeatured = $index === 1; // Make second package featured
                        ?>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card package-card <?php echo $isFeatured ? 'featured' : ''; ?>">
                                <div class="card-body text-center p-4">
                                    <h4 class="card-title mb-3"><?php echo htmlspecialchars($package['package_name']); ?></h4>
                                    
                                    <div class="price mb-3">
                                        ৳<?php echo number_format($package['price']); ?>
                                    </div>
                                    
                                    <p class="text-muted mb-4">
                                        <?php echo $package['duration_days']; ?> days duration<br>
                                        Up to <?php echo $package['max_ads']; ?> ads
                                    </p>
                                    
                                    <ul class="feature-list mb-4">
                                        <li><i class="fas fa-check"></i>Ad Analytics Dashboard</li>
                                        <li><i class="fas fa-check"></i>24/7 Support</li>
                                        <?php if (isset($features['targeting']) && $features['targeting']): ?>
                                            <li><i class="fas fa-check"></i>Audience Targeting</li>
                                        <?php endif; ?>
                                        <?php if (isset($features['featured']) && $features['featured']): ?>
                                            <li><i class="fas fa-star text-warning"></i>Featured Placement</li>
                                        <?php endif; ?>
                                        <?php if (isset($features['dedicated_support']) && $features['dedicated_support']): ?>
                                            <li><i class="fas fa-crown text-warning"></i>Dedicated Support</li>
                                        <?php endif; ?>
                                    </ul>
                                    
                                    <button class="btn btn-primary w-100" 
                                            onclick="openPaymentModal(<?php echo $package['id']; ?>, '<?php echo htmlspecialchars($package['package_name']); ?>', <?php echo $package['price']; ?>)">
                                        <i class="fas fa-shopping-cart me-2"></i>Purchase Package
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Payment Information -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>Payment Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>How to Pay:</h6>
                                        <ol>
                                            <li>Choose your preferred package</li>
                                            <li>Select payment method</li>
                                            <li>Send money to the provided account</li>
                                            <li>Enter transaction ID and submit</li>
                                            <li>Wait for admin verification</li>
                                        </ol>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Payment Methods:</h6>
                                        <div class="row">
                                            <?php foreach ($payment_methods as $method): ?>
                                                <div class="col-6 mb-2">
                                                    <span class="badge bg-primary"><?php echo htmlspecialchars($method['display_name']); ?></span>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-credit-card me-2"></i>Purchase Package
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <h4 id="selectedPackageName"></h4>
                            <div class="price" id="selectedPackagePrice"></div>
                        </div>
                        
                        <input type="hidden" id="packageId" name="package_id">
                        
                        <!-- Payment Method Selection -->
                        <div class="mb-4">
                            <label class="form-label">Select Payment Method:</label>
                            <div class="row">
                                <?php foreach ($payment_methods as $method): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="payment-method-card p-3" onclick="selectPaymentMethod('<?php echo $method['method_code']; ?>')">
                                            <input type="radio" name="payment_method" value="<?php echo $method['method_code']; ?>" 
                                                   id="method_<?php echo $method['method_code']; ?>" class="d-none">
                                            <div class="text-center">
                                                <h6><?php echo htmlspecialchars($method['display_name']); ?></h6>
                                                <?php 
                                                $details = json_decode($method['account_details'], true);
                                                if (isset($details['number'])): ?>
                                                    <small class="text-muted"><?php echo htmlspecialchars($details['number']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <!-- Payment Instructions -->
                        <div id="paymentInstructions" class="alert alert-info" style="display: none;">
                            <h6>Payment Instructions:</h6>
                            <div id="instructionText"></div>
                        </div>
                        
                        <!-- Transaction Details -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="transaction_id" class="form-label">Transaction ID *</label>
                                <input type="text" class="form-control" id="transaction_id" name="transaction_id" 
                                       placeholder="Enter transaction ID" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="payment_proof" class="form-label">Payment Proof (Optional)</label>
                                <textarea class="form-control" id="payment_proof" name="payment_proof" 
                                          placeholder="Additional payment details or screenshot description"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="purchase_package" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>Submit Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function openPaymentModal(packageId, packageName, price) {
            document.getElementById('packageId').value = packageId;
            document.getElementById('selectedPackageName').textContent = packageName;
            document.getElementById('selectedPackagePrice').textContent = '৳' + price.toLocaleString();
            
            new bootstrap.Modal(document.getElementById('paymentModal')).show();
        }
        
        function selectPaymentMethod(methodCode) {
            // Remove selected class from all cards
            document.querySelectorAll('.payment-method-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.currentTarget.classList.add('selected');
            
            // Check the radio button
            document.getElementById('method_' + methodCode).checked = true;
            
            // Show payment instructions
            showPaymentInstructions(methodCode);
        }
        
        function showPaymentInstructions(methodCode) {
            const instructions = {
                'bkash': 'Send money to: 01712345678 (Personal)<br>Type: Send Money<br>Reference: Your WhatsApp number',
                'nagad': 'Send money to: 01812345678 (Personal)<br>Type: Send Money<br>Reference: Your WhatsApp number',
                'rocket': 'Send money to: 01912345678 (Personal)<br>Type: Send Money<br>Reference: Your WhatsApp number',
                'google_pay': 'Send payment to: <EMAIL><br>Include your WhatsApp number in the note',
                'paypal': 'Send payment to: <EMAIL><br>Include your WhatsApp number in the note'
            };
            
            const instructionDiv = document.getElementById('paymentInstructions');
            const instructionText = document.getElementById('instructionText');
            
            if (instructions[methodCode]) {
                instructionText.innerHTML = instructions[methodCode];
                instructionDiv.style.display = 'block';
            } else {
                instructionDiv.style.display = 'none';
            }
        }
    </script>
</body>
</html>
