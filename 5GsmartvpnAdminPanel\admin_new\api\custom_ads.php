<?php
/**
 * 5G Smart VPN Admin Panel - Modern Custom Ads API
 * Returns random active custom ads for the mobile app
 */

require_once '../includes/config.php';

// Set JSON response header
header('Content-Type: application/json');

// HMAC request signing validation
$timestamp = $_GET['timestamp'] ?? null;
$signature = $_GET['signature'] ?? null;

if (!$timestamp || !$signature) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Missing authentication parameters",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

if (abs(time() - intval($timestamp)) > 300) { // 5 minutes window
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Request timestamp expired",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

$computedSignature = hash_hmac('sha256', $timestamp, API_KEY);
if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    echo json_encode([
        "error" => "Unauthorized",
        "message" => "Invalid request signature",
        "code" => 401,
        "timestamp" => time()
    ]);
    exit;
}

try {
    // Get current date for filtering active ads
    $today = date("Y-m-d");

    // Query to get active custom ads using MySQLi with enhanced fields
    $stmt = $conn->prepare("SELECT *,
        CASE
            WHEN expires_at IS NOT NULL AND expires_at < NOW() THEN 0
            ELSE 1
        END as is_active_now
        FROM custom_ads
        WHERE `on` = 1
        AND date_start <= ?
        AND date_end >= ?
        AND is_approved = 1
        AND (expires_at IS NULL OR expires_at > NOW())
        ORDER BY priority DESC, RAND()");
    $stmt->bind_param("ss", $today, $today);
    $stmt->execute();
    $result = $stmt->get_result();

    $custom_ads_list = [];
    while ($row = $result->fetch_assoc()) {
        // Auto-detect URL type if not set
        if (empty($row['url_type']) || $row['url_type'] === 'other') {
            $url = strtolower($row['url']);
            if (strpos($url, 'play.google.com') !== false) {
                $row['url_type'] = 'playstore';
                $row['button_text'] = 'Install';
            } elseif (strpos($url, 'apps.apple.com') !== false || strpos($url, 'itunes.apple.com') !== false) {
                $row['url_type'] = 'appstore';
                $row['button_text'] = 'Install';
            } else {
                $row['url_type'] = 'website';
                $row['button_text'] = 'Visit';
            }
        }

        // Ensure button_text is set
        if (empty($row['button_text'])) {
            $row['button_text'] = $row['url_type'] === 'playstore' || $row['url_type'] === 'appstore' ? 'Install' : 'Visit';
        }

        $custom_ads_list[] = $row;
    }

    $response_data = [];

    if (count($custom_ads_list) > 0) {
        // If there are running ads, select a random one
        $random_index = array_rand($custom_ads_list);
        $random_ad = $custom_ads_list[$random_index];

        // Normalize field names for consistency with Android app
        $normalized_ad = [
            'id' => $random_ad['id'],
            'title' => $random_ad['title'],
            'image' => $random_ad['image'], // Database column is 'image'
            'text' => $random_ad['text'],   // Database column is 'text'
            'url' => $random_ad['url'],
            'url_type' => $random_ad['url_type'], // New field for URL type detection
            'button_text' => $random_ad['button_text'], // New field for button text
            'date_start' => $random_ad['date_start'],
            'date_end' => $random_ad['date_end'],
            'expires_at' => $random_ad['expires_at'],
            'priority' => (int)$random_ad['priority'],
            'on' => $random_ad['on'],
            'view_count' => $random_ad['view_count'],
            'click_count' => $random_ad['click_count'],
            'is_approved' => (bool)$random_ad['is_approved'],
            // Add metadata to the ad
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'custom_ads',
            'total_active_ads' => count($custom_ads_list),
            // Enhanced metadata
            'smart_url_detection' => true,
            'supports_app_store_detection' => true
        ];

        $response_data[] = $normalized_ad;
    } else {
        // If no running ads, return empty array with metadata
        $response_data = [
            'message' => 'No active custom ads found',
            'api_version' => '3.0',
            'timestamp' => time(),
            'source' => 'modern_admin_panel',
            'endpoint' => 'custom_ads',
            'total_active_ads' => 0
        ];
    }

    echo json_encode($response_data);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "error" => "Internal Server Error",
        "message" => "Failed to fetch custom ads",
        "code" => 500,
        "timestamp" => time(),
        "debug" => $e->getMessage()
    ]);
}
?>
