<libraries>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28c1791a3326d74d2abe075aa0077ed9\transformed\constraintlayout-2.0.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.4"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28c1791a3326d74d2abe075aa0077ed9\transformed\constraintlayout-2.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2bbf751d9977a2aba6445c756e9f5f\transformed\appcompat-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df2bbf751d9977a2aba6445c756e9f5f\transformed\appcompat-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads:20.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4389988ef424daa977621a51e10471c8\transformed\play-services-ads-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads:20.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4389988ef424daa977621a51e10471c8\transformed\play-services-ads-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.16@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.16\3fdb501b45ba22c6e9c0f2abdb6ed747a48c71af\error_prone_annotations-2.16.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.16"/>
  <library
      name="com.google.android.gms:play-services-ads-lite:20.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7855769a8439283e20150d39989ec94\transformed\play-services-ads-lite-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-lite:20.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7855769a8439283e20150d39989ec94\transformed\play-services-ads-lite-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-base:20.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25c0844c96b409888031dea251c5aca7\transformed\play-services-ads-base-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-base:20.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25c0844c96b409888031dea251c5aca7\transformed\play-services-ads-base-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.ump:user-messaging-platform:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edef556579803bb684c63a7f13a04189\transformed\user-messaging-platform-1.0.0\jars\classes.jar"
      resolved="com.google.android.ump:user-messaging-platform:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edef556579803bb684c63a7f13a04189\transformed\user-messaging-platform-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b34d988b95b4e91f92e1936199b63e69\transformed\play-services-ads-identifier-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b34d988b95b4e91f92e1936199b63e69\transformed\play-services-ads-identifier-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd3f362339e1d96dc3966cc58e2ed5b\transformed\play-services-tasks-17.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd3f362339e1d96dc3966cc58e2ed5b\transformed\play-services-tasks-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9acc19ee86e867bbdeeff5b8bdbcabd7\transformed\play-services-measurement-sdk-api-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9acc19ee86e867bbdeeff5b8bdbcabd7\transformed\play-services-measurement-sdk-api-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1dde8d7f1c884b6d11707266efe26fa6\transformed\play-services-measurement-base-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1dde8d7f1c884b6d11707266efe26fa6\transformed\play-services-measurement-base-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:17.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74abc7da3b3146c7468be9fe9f4dcc7d\transformed\play-services-basement-17.6.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:17.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74abc7da3b3146c7468be9fe9f4dcc7d\transformed\play-services-basement-17.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ae363edc2b540cd0f58fc192777f712\transformed\fragment-1.1.0\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ae363edc2b540cd0f58fc192777f712\transformed\fragment-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec0bee16281c76543cb3f5ad8b40b16c\transformed\appcompat-resources-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec0bee16281c76543cb3f5ad8b40b16c\transformed\appcompat-resources-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fce16728afe9f4aa276c1a0d794d1170\transformed\browser-1.0.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fce16728afe9f4aa276c1a0d794d1170\transformed\browser-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04cd26c304bd13f8d343757966da7ec6\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04cd26c304bd13f8d343757966da7ec6\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91e2bdba1986a498ec6a9f65148705ad\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91e2bdba1986a498ec6a9f65148705ad\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\591458ac8ab581f1052b5ac57db2c64a\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\591458ac8ab581f1052b5ac57db2c64a\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17d999135f56a39db3465a5bd18a3919\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17d999135f56a39db3465a5bd18a3919\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\643de6fdf3e9c6dd0a91bce6c7235969\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\643de6fdf3e9c6dd0a91bce6c7235969\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\656edfeab0293cd0506e6775c005d124\transformed\activity-1.0.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\656edfeab0293cd0506e6775c005d124\transformed\activity-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2343d7ce6ad0586a3a200159af0ab619\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2343d7ce6ad0586a3a200159af0ab619\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9cc17d8313747e2151c9666bd4afa26\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9cc17d8313747e2151c9666bd4afa26\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bccc0ec2df8fc0ddab250c1f787aab3a\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bccc0ec2df8fc0ddab250c1f787aab3a\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bcade37b14ece138db1f69246370e54\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bcade37b14ece138db1f69246370e54\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\686a383066dab3a95152ffb51561757d\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\686a383066dab3a95152ffb51561757d\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\427a5512a27428cc5c4e31fda66dd18b\transformed\work-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\427a5512a27428cc5c4e31fda66dd18b\transformed\work-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6d16191694fab691ad6bab215f7da0d\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6d16191694fab691ad6bab215f7da0d\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acd7aa0d6ceeb0445f0646781bce8f7f\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acd7aa0d6ceeb0445f0646781bce8f7f\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a2b1404d8db6eff7e5d228906a0c06a\transformed\room-runtime-2.1.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a2b1404d8db6eff7e5d228906a0c06a\transformed\room-runtime-2.1.0\jars\libs\room-common-java8-2.1.0.jar"
      resolved="androidx.room:room-runtime:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a2b1404d8db6eff7e5d228906a0c06a\transformed\room-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3dcb01d4da2a11bda29288d6f61c71f\transformed\core-1.3.1\jars\classes.jar"
      resolved="androidx.core:core:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3dcb01d4da2a11bda29288d6f61c71f\transformed\core-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7cca71a968b4d87b13dbb9802987133\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7cca71a968b4d87b13dbb9802987133\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48df1142b3b30f96f0df069b1e2c0e09\transformed\versionedparcelable-1.1.0\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48df1142b3b30f96f0df069b1e2c0e09\transformed\versionedparcelable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b337ccdb1bae86ed5d5864d889cb3b0a\transformed\lifecycle-service-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b337ccdb1bae86ed5d5864d889cb3b0a\transformed\lifecycle-service-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe37837e87d5eab370d895299d08470d\transformed\lifecycle-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe37837e87d5eab370d895299d08470d\transformed\lifecycle-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79f0500df198745a2cc34abfe652b557\transformed\lifecycle-viewmodel-2.1.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79f0500df198745a2cc34abfe652b557\transformed\lifecycle-viewmodel-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d36666e9714db33183b9e3e6c7674\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf1d36666e9714db33183b9e3e6c7674\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71b30d09ce8a46b3594895a15f30e1cc\transformed\savedstate-1.0.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71b30d09ce8a46b3594895a15f30e1cc\transformed\savedstate-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5255f3ba261b8721f36d5cdc6e51fef7\transformed\lifecycle-livedata-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5255f3ba261b8721f36d5cdc6e51fef7\transformed\lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2654550d0103dab4542575a3427a342e\transformed\lifecycle-livedata-core-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2654550d0103dab4542575a3427a342e\transformed\lifecycle-livedata-core-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.1.0\c67e7807d9cd6c329b9d0218b2ec4e505dd340b7\lifecycle-common-2.1.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\833fbcac1a149303ffdd1f1125658449\transformed\core-runtime-2.0.1\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\833fbcac1a149303ffdd1f1125658449\transformed\core-runtime-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.1.0\b3152fc64428c9354344bd89848ecddc09b6f07e\core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a87b433c13b3b1bc3bdfaf7793e50a\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8a87b433c13b3b1bc3bdfaf7793e50a\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79641da59d7c3bf77742ee60a2924b13\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79641da59d7c3bf77742ee60a2924b13\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88c0065b9a5c050c2afa1471d1f0af70\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88c0065b9a5c050c2afa1471d1f0af70\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.1.0\b87765704590bd992ea0d92ac50253a9df7818a0\room-common-2.1.0.jar"
      resolved="androidx.room:room-common:2.1.0"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3105a0d34f0a591e188fd52bed543ff\transformed\sqlite-framework-2.0.1\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3105a0d34f0a591e188fd52bed543ff\transformed\sqlite-framework-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a366470acaf86957268a301ee814683\transformed\sqlite-2.0.1\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a366470acaf86957268a301ee814683\transformed\sqlite-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation\1.1.0\e3a6fb2f40e3a3842e6b7472628ba4ce416ea4c8\annotation-1.1.0.jar"
      resolved="androidx.annotation:annotation:1.1.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.4\1f001d7db280a89a6c26b26a66eb064bb6d5efeb\constraintlayout-solver-2.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.4"/>
</libraries>
