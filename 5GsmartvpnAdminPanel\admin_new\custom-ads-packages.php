<?php
/**
 * Admin Panel - Custom Ads Packages Management
 * Manage ad packages, pricing, and features
 */

require_once 'includes/auth.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

$page_title = "Custom Ads Packages";
$success = '';
$error = '';

// Handle package operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_package'])) {
        $package_name = trim($_POST['package_name']);
        $duration_days = (int)$_POST['duration_days'];
        $price = (float)$_POST['price'];
        $currency = $_POST['currency'];
        $max_ads = (int)$_POST['max_ads'];
        $features = json_encode([
            'priority' => (int)$_POST['priority'],
            'analytics' => isset($_POST['analytics']),
            'support' => $_POST['support_level'],
            'targeting' => isset($_POST['targeting']),
            'featured' => isset($_POST['featured']),
            'dedicated_support' => isset($_POST['dedicated_support'])
        ]);
        $is_active = isset($_POST['is_active']);
        $display_order = (int)$_POST['display_order'];
        
        $stmt = $conn->prepare("INSERT INTO ad_packages (package_name, duration_days, price, currency, max_ads, features, is_active, display_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sidsissi", $package_name, $duration_days, $price, $currency, $max_ads, $features, $is_active, $display_order);
        
        if ($stmt->execute()) {
            $success = "Package added successfully!";
        } else {
            $error = "Failed to add package.";
        }
    }
    
    if (isset($_POST['update_package'])) {
        $package_id = (int)$_POST['package_id'];
        $package_name = trim($_POST['package_name']);
        $duration_days = (int)$_POST['duration_days'];
        $price = (float)$_POST['price'];
        $currency = $_POST['currency'];
        $max_ads = (int)$_POST['max_ads'];
        $features = json_encode([
            'priority' => (int)$_POST['priority'],
            'analytics' => isset($_POST['analytics']),
            'support' => $_POST['support_level'],
            'targeting' => isset($_POST['targeting']),
            'featured' => isset($_POST['featured']),
            'dedicated_support' => isset($_POST['dedicated_support'])
        ]);
        $is_active = isset($_POST['is_active']);
        $display_order = (int)$_POST['display_order'];
        
        $stmt = $conn->prepare("UPDATE ad_packages SET package_name = ?, duration_days = ?, price = ?, currency = ?, max_ads = ?, features = ?, is_active = ?, display_order = ? WHERE id = ?");
        $stmt->bind_param("sidsissii", $package_name, $duration_days, $price, $currency, $max_ads, $features, $is_active, $display_order, $package_id);
        
        if ($stmt->execute()) {
            $success = "Package updated successfully!";
        } else {
            $error = "Failed to update package.";
        }
    }
    
    if (isset($_POST['delete_package'])) {
        $package_id = (int)$_POST['package_id'];
        
        // Check if package is being used
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM customer_payments WHERE package_id = ?");
        $stmt->bind_param("i", $package_id);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        if ($result['count'] > 0) {
            $error = "Cannot delete package. It has associated payments.";
        } else {
            $stmt = $conn->prepare("DELETE FROM ad_packages WHERE id = ?");
            $stmt->bind_param("i", $package_id);
            
            if ($stmt->execute()) {
                $success = "Package deleted successfully!";
            } else {
                $error = "Failed to delete package.";
            }
        }
    }
}

// Get all packages
$packages_result = mysqli_query($conn, "SELECT * FROM ad_packages ORDER BY display_order ASC, created_at DESC");
$packages = mysqli_fetch_all($packages_result, MYSQLI_ASSOC);

// Get package statistics
$stats = [];
$stats_result = mysqli_query($conn, "
    SELECT 
        ap.id,
        ap.package_name,
        COUNT(cp.id) as total_purchases,
        SUM(CASE WHEN cp.payment_status = 'verified' THEN cp.amount ELSE 0 END) as total_revenue,
        COUNT(CASE WHEN cp.payment_status = 'pending' THEN 1 END) as pending_payments
    FROM ad_packages ap
    LEFT JOIN customer_payments cp ON ap.id = cp.package_id
    GROUP BY ap.id, ap.package_name
");
while ($row = mysqli_fetch_assoc($stats_result)) {
    $stats[$row['id']] = $row;
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Custom Ads Packages</h4>
                <div class="page-title-right">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPackageModal">
                        <i class="ri-add-line me-1"></i>Add Package
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Packages Overview -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Package Overview</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Package Name</th>
                                    <th>Duration</th>
                                    <th>Price</th>
                                    <th>Max Ads</th>
                                    <th>Features</th>
                                    <th>Status</th>
                                    <th>Statistics</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($packages as $package): ?>
                                    <?php 
                                    $features = json_decode($package['features'], true) ?? [];
                                    $package_stats = $stats[$package['id']] ?? ['total_purchases' => 0, 'total_revenue' => 0, 'pending_payments' => 0];
                                    ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($package['package_name']); ?></strong>
                                            <br><small class="text-muted">Order: <?php echo $package['display_order']; ?></small>
                                        </td>
                                        <td><?php echo $package['duration_days']; ?> days</td>
                                        <td>
                                            <strong><?php echo $package['currency']; ?> <?php echo number_format($package['price'], 2); ?></strong>
                                        </td>
                                        <td><?php echo $package['max_ads']; ?> ads</td>
                                        <td>
                                            <div class="d-flex flex-wrap gap-1">
                                                <?php if (isset($features['analytics']) && $features['analytics']): ?>
                                                    <span class="badge bg-info">Analytics</span>
                                                <?php endif; ?>
                                                <?php if (isset($features['targeting']) && $features['targeting']): ?>
                                                    <span class="badge bg-warning">Targeting</span>
                                                <?php endif; ?>
                                                <?php if (isset($features['featured']) && $features['featured']): ?>
                                                    <span class="badge bg-success">Featured</span>
                                                <?php endif; ?>
                                                <?php if (isset($features['dedicated_support']) && $features['dedicated_support']): ?>
                                                    <span class="badge bg-primary">Dedicated Support</span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted">Priority: <?php echo $features['priority'] ?? 1; ?></small>
                                        </td>
                                        <td>
                                            <?php if ($package['is_active']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small>
                                                Purchases: <?php echo $package_stats['total_purchases']; ?><br>
                                                Revenue: ৳<?php echo number_format($package_stats['total_revenue'], 2); ?><br>
                                                Pending: <?php echo $package_stats['pending_payments']; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editPackage(<?php echo htmlspecialchars(json_encode($package)); ?>)">
                                                    <i class="ri-edit-line"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deletePackage(<?php echo $package['id']; ?>, '<?php echo htmlspecialchars($package['package_name']); ?>')">
                                                    <i class="ri-delete-line"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Package Modal -->
<div class="modal fade" id="addPackageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Package</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Package Name</label>
                            <input type="text" class="form-control" name="package_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Duration (Days)</label>
                            <input type="number" class="form-control" name="duration_days" min="1" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Price</label>
                            <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Currency</label>
                            <select class="form-select" name="currency" required>
                                <option value="BDT">BDT</option>
                                <option value="USD">USD</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Max Ads</label>
                            <input type="number" class="form-control" name="max_ads" min="1" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Priority</label>
                            <input type="number" class="form-control" name="priority" min="1" max="10" value="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Display Order</label>
                            <input type="number" class="form-control" name="display_order" min="0" value="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Support Level</label>
                        <select class="form-select" name="support_level" required>
                            <option value="basic">Basic</option>
                            <option value="standard">Standard</option>
                            <option value="premium">Premium</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Features</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="analytics" id="analytics" checked>
                                    <label class="form-check-label" for="analytics">Analytics Dashboard</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="targeting" id="targeting">
                                    <label class="form-check-label" for="targeting">Audience Targeting</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="featured" id="featured">
                                    <label class="form-check-label" for="featured">Featured Placement</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="dedicated_support" id="dedicated_support">
                                    <label class="form-check-label" for="dedicated_support">Dedicated Support</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_package" class="btn btn-primary">Add Package</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Package Modal -->
<div class="modal fade" id="editPackageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Package</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editPackageForm">
                <input type="hidden" name="package_id" id="edit_package_id">
                <!-- Same form fields as add modal but with edit_ prefix for IDs -->
                <div class="modal-body">
                    <!-- Form fields similar to add modal -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_package" class="btn btn-primary">Update Package</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPackage(packageData) {
    // Populate edit modal with package data
    document.getElementById('edit_package_id').value = packageData.id;
    // ... populate other fields
    new bootstrap.Modal(document.getElementById('editPackageModal')).show();
}

function deletePackage(packageId, packageName) {
    if (confirm('Are you sure you want to delete the package "' + packageName + '"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="package_id" value="' + packageId + '">' +
                        '<input type="hidden" name="delete_package" value="1">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
