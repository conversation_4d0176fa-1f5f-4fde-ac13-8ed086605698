<dependencies>
  <compile
      roots="androidx.constraintlayout:constraintlayout:2.0.4@aar,androidx.appcompat:appcompat:1.2.0@aar,com.google.android.gms:play-services-ads:20.1.0@aar,com.google.errorprone:error_prone_annotations:2.16@jar,com.google.android.gms:play-services-ads-lite:20.1.0@aar,com.google.android.gms:play-services-ads-base:20.1.0@aar,com.google.android.ump:user-messaging-platform:1.0.0@aar,com.google.android.gms:play-services-ads-identifier:17.0.0@aar,com.google.android.gms:play-services-tasks:17.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:18.0.0@aar,com.google.android.gms:play-services-measurement-base:18.0.0@aar,com.google.android.gms:play-services-basement:17.6.0@aar,androidx.fragment:fragment:1.1.0@aar,androidx.appcompat:appcompat-resources:1.2.0@aar,androidx.browser:browser:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.work:work-runtime:2.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.room:room-runtime:2.1.0@aar,androidx.core:core:1.3.1@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.0@aar,androidx.collection:collection:1.1.0@jar,androidx.lifecycle:lifecycle-service:2.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.1.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.savedstate:savedstate:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar,androidx.lifecycle:lifecycle-common:2.1.0@jar,androidx.arch.core:core-runtime:2.0.1@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.room:room-common:2.1.0@jar,androidx.sqlite:sqlite-framework:2.0.1@aar,androidx.sqlite:sqlite:2.0.1@aar,androidx.annotation:annotation:1.1.0@jar,com.google.guava:listenablefuture:1.0@jar,androidx.constraintlayout:constraintlayout-solver:2.0.4@jar">
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-ads:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.16@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.android.gms:play-services-ads-lite:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-ads-lite"/>
    <dependency
        name="com.google.android.gms:play-services-ads-base:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-ads-base"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:1.0.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:17.6.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.1.0@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.browser:browser:1.0.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.0.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.work:work-runtime:2.1.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.room:room-runtime:2.1.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.core:core:1.3.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.0@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.1.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.savedstate:savedstate:1.0.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.1.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.0.1@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.room:room-common:2.1.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.0.1@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.0.1@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.annotation:annotation:1.1.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
  </compile>
  <package
      roots="androidx.constraintlayout:constraintlayout:2.0.4@aar,androidx.appcompat:appcompat:1.2.0@aar,com.google.android.gms:play-services-ads:20.1.0@aar,com.google.errorprone:error_prone_annotations:2.16@jar,androidx.browser:browser:1.0.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,com.google.android.gms:play-services-ads-lite:20.1.0@aar,com.google.android.gms:play-services-ads-base:20.1.0@aar,com.google.android.ump:user-messaging-platform:1.0.0@aar,com.google.android.gms:play-services-ads-identifier:17.0.0@aar,com.google.android.gms:play-services-tasks:17.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:18.0.0@aar,com.google.android.gms:play-services-measurement-base:18.0.0@aar,com.google.android.gms:play-services-basement:17.6.0@aar,androidx.fragment:fragment:1.1.0@aar,androidx.appcompat:appcompat-resources:1.2.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.work:work-runtime:2.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.room:room-runtime:2.1.0@aar,androidx.core:core:1.3.1@aar,androidx.versionedparcelable:versionedparcelable:1.1.0@aar,androidx.collection:collection:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.lifecycle:lifecycle-service:2.0.0@aar,androidx.lifecycle:lifecycle-runtime:2.1.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar,androidx.savedstate:savedstate:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar,androidx.arch.core:core-runtime:2.0.1@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.lifecycle:lifecycle-common:2.1.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.room:room-common:2.1.0@jar,androidx.sqlite:sqlite-framework:2.0.1@aar,androidx.sqlite:sqlite:2.0.1@aar,androidx.annotation:annotation:1.1.0@jar,androidx.constraintlayout:constraintlayout-solver:2.0.4@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.google.android.gms:play-services-ads:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-ads"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.16@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.browser:browser:1.0.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="com.google.android.gms:play-services-ads-lite:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-ads-lite"/>
    <dependency
        name="com.google.android.gms:play-services-ads-base:20.1.0@aar"
        simpleName="com.google.android.gms:play-services-ads-base"/>
    <dependency
        name="com.google.android.ump:user-messaging-platform:1.0.0@aar"
        simpleName="com.google.android.ump:user-messaging-platform"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:17.0.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:17.6.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.1.0@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.2.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.0.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.work:work-runtime:2.1.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.room:room-runtime:2.1.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.core:core:1.3.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.0@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.1.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.savedstate:savedstate:1.0.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.0.1@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.1.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.room:room-common:2.1.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.0.1@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.0.1@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.annotation:annotation:1.1.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
