<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="conditional_incidents">

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="12809"
                    endOffset="12810"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="12809"
                    endOffset="12810"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="320"
            column="80"
            startOffset="12809"
            endLine="320"
            endColumn="81"
            endOffset="12810"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="17658"
                    endOffset="17659"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="17658"
                    endOffset="17659"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="445"
            column="89"
            startOffset="17658"
            endLine="445"
            endColumn="90"
            endOffset="17659"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="18343"
                    endOffset="18344"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="18343"
                    endOffset="18344"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="458"
            column="80"
            startOffset="18343"
            endLine="458"
            endColumn="81"
            endOffset="18344"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="18874"
                    endOffset="18875"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="18874"
                    endOffset="18875"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="468"
            column="80"
            startOffset="18874"
            endLine="468"
            endColumn="81"
            endOffset="18875"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19398"
                    endOffset="19399"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19398"
                    endOffset="19399"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="480"
            column="77"
            startOffset="19398"
            endLine="480"
            endColumn="78"
            endOffset="19399"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20096"
                    endOffset="20097"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20096"
                    endOffset="20097"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="501"
            column="66"
            startOffset="20096"
            endLine="501"
            endColumn="67"
            endOffset="20097"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="53548"
                    endOffset="53549"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="53548"
                    endOffset="53549"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="1360"
            column="76"
            startOffset="53548"
            endLine="1360"
            endColumn="77"
            endOffset="53549"/>
    </incident>

</incidents>
